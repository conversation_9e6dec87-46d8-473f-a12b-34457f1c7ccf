import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { toast } from "react-toastify";
import ComboBox from "~/core/components/ComboBox";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import { clientOptions } from "~/modules/client/hooks/client-options";
import { workerOptions } from "~/modules/worker/hooks/worker-options";
import useCreateSession from "~/modules/session/hooks/use-create-session";
import type { Client } from "~/modules/client/service/model/client";
import type { Worker } from "~/modules/worker/service/model/worker";
import type { Session } from "~/modules/session/service/model/session";

interface SessionModalProps {
	isOpen: boolean;
	onClose: () => void;
	turnId: string;
	dayIndex: number;
	timeIndex: number;
	preSelectedClient: Client | null;
	preSelectedWorker: Worker | null;
	existingSession: Session | undefined;
}

export default function SessionModal({
	isOpen,
	onClose,
	turnId,
	dayIndex,
	timeIndex,
	preSelectedClient,
	preSelectedWorker,
	existingSession,
}: SessionModalProps) {
	const svc = useService();
	const { mutate: createSession } = useCreateSession();
	
	const [selectedClient, setSelectedClient] = useState<Client | null>(null);
	const [selectedWorker, setSelectedWorker] = useState<Worker | null>(null);

	const {
		data: clients,
		isError: clientsError,
		error: clientError,
		isPending: clientsPending,
	} = useQuery({
		...clientOptions(svc),
		enabled: isOpen,
	});

	const {
		data: workers,
		isError: workersError,
		error: workerError,
		isPending: workersPending,
	} = useQuery({
		...workerOptions(svc),
		enabled: isOpen,
	});

	// Set initial values when modal opens
	useEffect(() => {
		if (isOpen) {
			if (existingSession) {
				setSelectedClient(existingSession.client);
				setSelectedWorker(existingSession.worker);
			} else {
				setSelectedClient(preSelectedClient);
				setSelectedWorker(preSelectedWorker);
			}
		}
	}, [isOpen, existingSession, preSelectedClient, preSelectedWorker]);

	// Reset when modal closes
	useEffect(() => {
		if (!isOpen) {
			setSelectedClient(null);
			setSelectedWorker(null);
		}
	}, [isOpen]);

	useEffect(() => {
		if (clientError) {
			console.log(getErrorResult(clientError).error);
		}
		if (workerError) {
			console.log(getErrorResult(workerError).error);
		}
	}, [clientError, workerError]);

	const clientOptions_data = clients?.map((client) => ({
		value: client.id,
		label: `${client.person.name} ${client.person.fatherLastName} ${client.person.motherLastName}`,
		data: client,
	})) || [];

	const workerOptions_data = workers?.map((worker) => ({
		value: worker.id,
		label: `${worker.person.name} ${worker.person.fatherLastName} ${worker.person.motherLastName}`,
		data: worker,
	})) || [];

	const handleClientChange = (option: { value: string; label: string; data: Client } | null) => {
		setSelectedClient(option?.data || null);
	};

	const handleWorkerChange = (option: { value: string; label: string; data: Worker } | null) => {
		setSelectedWorker(option?.data || null);
	};

	const handleSubmit = () => {
		if (!selectedClient || !selectedWorker) {
			toast.error("Debe seleccionar un cliente y un trabajador");
			return;
		}

		createSession(
			{
				clientId: selectedClient.id,
				workerId: selectedWorker.id,
				turnId,
				day: dayIndex,
				time: timeIndex,
			},
			{
				onSuccess: () => {
					toast.success("Sesión creada exitosamente");
					onClose();
				},
				onError: (error) => {
					console.log(error);
					const { error: errorResult } = getErrorResult(error);
					toast.error(errorResult.message);
				},
			}
		);
	};

	const selectedClientOption = selectedClient ? {
		value: selectedClient.id,
		label: `${selectedClient.person.name} ${selectedClient.person.fatherLastName} ${selectedClient.person.motherLastName}`,
		data: selectedClient,
	} : null;

	const selectedWorkerOption = selectedWorker ? {
		value: selectedWorker.id,
		label: `${selectedWorker.person.name} ${selectedWorker.person.fatherLastName} ${selectedWorker.person.motherLastName}`,
		data: selectedWorker,
	} : null;

	const days = ["Domingo", "Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado"];

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={onClose} />
				<h3 className="font-bold text-lg">
					{existingSession ? "Editar Sesión" : "Crear Sesión"}
				</h3>

				<div className="py-4">
					<p className="mb-4 text-base-content/70 text-sm">
						{days[dayIndex]} - Horario {timeIndex + 1}
					</p>

					{!existingSession && (preSelectedClient || preSelectedWorker) && (
						<div className="mb-4 rounded-lg bg-base-200 p-3">
							<p className="font-medium text-sm">
								{preSelectedClient
									? `Creando sesión para: ${preSelectedClient.person.name} ${preSelectedClient.person.fatherLastName}`
									: `Creando sesión con: ${preSelectedWorker?.person.name} ${preSelectedWorker?.person.fatherLastName}`
								}
							</p>
						</div>
					)}

					<div className="space-y-4">
						{/* Show Client Selection only if no worker was pre-selected or if editing existing session */}
						{(!preSelectedWorker || existingSession) && (
							<div className="form-control">
								<label htmlFor="session-client-combobox" className="label">
									<span className="label-text">Cliente</span>
								</label>
								{clientsError ? (
									<div className="alert alert-error">
										<span>Error: {getErrorResult(clientError).error.message}</span>
									</div>
								) : (
									<ComboBox
										options={clientOptions_data}
										value={selectedClientOption}
										onChange={handleClientChange}
										placeholder="Buscar cliente..."
										isLoading={clientsPending}
										label="Cliente"
									/>
								)}
							</div>
						)}

						{/* Show Worker Selection only if no client was pre-selected or if editing existing session */}
						{(!preSelectedClient || existingSession) && (
							<div className="form-control">
								<label htmlFor="session-worker-combobox" className="label">
									<span className="label-text">Trabajador</span>
								</label>
								{workersError ? (
									<div className="alert alert-error">
										<span>Error: {getErrorResult(workerError).error.message}</span>
									</div>
								) : (
									<ComboBox
										options={workerOptions_data}
										value={selectedWorkerOption}
										onChange={handleWorkerChange}
										placeholder="Buscar trabajador..."
										isLoading={workersPending}
										label="Trabajador"
									/>
								)}
							</div>
						)}

						{/* Show message when only one field is available */}
						{preSelectedClient && !existingSession && (
							<div className="alert alert-info">
								<span>
									Cliente seleccionado: {preSelectedClient.person.name} {preSelectedClient.person.fatherLastName}.
									Selecciona un trabajador para esta sesión.
								</span>
							</div>
						)}

						{preSelectedWorker && !existingSession && (
							<div className="alert alert-info">
								<span>
									Trabajador seleccionado: {preSelectedWorker.person.name} {preSelectedWorker.person.fatherLastName}.
									Selecciona un cliente para esta sesión.
								</span>
							</div>
						)}
					</div>
				</div>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-ghost"
						onClick={onClose}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-primary"
						onClick={handleSubmit}
						disabled={!selectedClient || !selectedWorker}
					>
						{existingSession ? "Actualizar" : "Crear"} Sesión
					</button>
				</div>
			</div>
		</div>
	);
}
