import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import type { Schedule, Turn } from "~/modules/schedule/service/model/schedule";
import type { Client } from "~/modules/client/service/model/client";
import type { Worker } from "~/modules/worker/service/model/worker";
import { useService } from "~/config/context/serviceProvider";
import { sessionOptions } from "~/modules/session/hooks/session-options";
import SessionModal from "./SessionModal";

interface SessionScheduleGridProps {
	schedule: Schedule;
	turn: Turn;
	selectedClient: Client | null;
	selectedWorker: Worker | null;
}

interface TimeSlot {
	start: number;
	end: number;
	label: string;
}

interface SessionModalData {
	dayIndex: number;
	timeIndex: number;
	isOpen: boolean;
}

export default function SessionScheduleGrid({
	schedule,
	turn,
	selectedClient,
	selectedWorker,
}: SessionScheduleGridProps) {
	const svc = useService();
	const [sessionModal, setSessionModal] = useState<SessionModalData>({
		dayIndex: -1,
		timeIndex: -1,
		isOpen: false,
	});

	const { data: sessions } = useQuery(sessionOptions(svc));

	const formatTime = (time: number) => {
		const hours = Math.floor(time / 100);
		const minutes = time % 100;
		return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
	};

	const minutesToTimeString = (minutes: number) => {
		const hours = Math.floor(minutes / 60);
		const mins = minutes % 60;
		const period = hours >= 12 ? "PM" : "AM";
		const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours;
		return `${displayHours}:${mins.toString().padStart(2, "0")} ${period}`;
	};

	const generateTimeSlots = (): TimeSlot[] => {
		const slots: TimeSlot[] = [];
		const sessionDuration = schedule.sessionDuration;
		const breakDuration = schedule.breakDuration;

		// Convert start and end times to minutes
		const startTimeInMinutes = Math.floor(turn.startTime / 100) * 60 + (turn.startTime % 100);
		const endTimeInMinutes = Math.floor(turn.endTime / 100) * 60 + (turn.endTime % 100);

		let currentTime = startTimeInMinutes;

		while (currentTime < endTimeInMinutes) {
			const sessionEnd = currentTime + sessionDuration;

			// Check if the complete session fits within the time window
			if (sessionEnd > endTimeInMinutes) {
				break; // Not enough time for a complete session
			}

			slots.push({
				start: currentTime,
				end: sessionEnd,
				label: "Sesión",
			});

			// Move to next session time (including break duration for spacing)
			currentTime = sessionEnd + breakDuration;
		}

		return slots;
	};

	const timeSlots = generateTimeSlots();
	const days = [
		"Domingo",
		"Lunes",
		"Martes",
		"Miércoles",
		"Jueves",
		"Viernes",
		"Sábado",
	];

	const getSessionForSlot = (dayIndex: number, timeIndex: number) => {
		return sessions?.find(
			(session) =>
				session.turnId === turn.id &&
				session.day === dayIndex &&
				session.time === timeIndex
		);
	};

	const handleCellClick = (dayIndex: number, timeIndex: number) => {
		setSessionModal({
			dayIndex,
			timeIndex,
			isOpen: true,
		});
	};

	const handleModalClose = () => {
		setSessionModal({
			dayIndex: -1,
			timeIndex: -1,
			isOpen: false,
		});
	};

	return (
		<>
			<div className="overflow-x-auto">
				<div
					className="grid gap-1 text-xs"
					style={{
						gridTemplateColumns: `120px repeat(${days.length}, 1fr)`,
						gridTemplateRows: `auto repeat(${timeSlots.length}, minmax(64px, auto))`,
					}}
				>
					{/* Header row */}
					<div className="rounded bg-base-200 p-2 font-medium">Hora</div>
					{days.map((day) => (
						<div
							key={day}
							className="rounded bg-base-200 p-2 text-center font-medium"
						>
							{day}
						</div>
					))}

					{/* Time slots and content */}
					{timeSlots.map((slot, timeIndex) => (
						<React.Fragment key={`${slot.start}-${timeIndex}`}>
							<div className="flex items-center rounded bg-base-100 p-2 font-mono text-xs">
								{minutesToTimeString(slot.start)} -{" "}
								{minutesToTimeString(slot.end)}
							</div>
							{days.map((day, dayIndex) => {
								const existingSession = getSessionForSlot(dayIndex, timeIndex);
								return (
									<div
										key={day}
										className="flex items-center justify-center p-1"
									>
										<button
											type="button"
											className={`flex h-16 w-full items-center justify-center rounded px-2 py-1 font-medium text-xs transition-colors ${
												existingSession
													? "bg-success text-success-content hover:bg-success/80"
													: "bg-primary text-primary-content hover:bg-primary/80"
											}`}
											onClick={() => handleCellClick(dayIndex, timeIndex)}
										>
											{existingSession ? (
												<div className="text-center">
													<div className="font-semibold">
														{existingSession.client.person.name}
													</div>
													<div className="text-xs opacity-80">
														{existingSession.worker.person.name}
													</div>
												</div>
											) : (
												"Disponible"
											)}
										</button>
									</div>
								);
							})}
						</React.Fragment>
					))}
				</div>
			</div>

			<SessionModal
				isOpen={sessionModal.isOpen}
				onClose={handleModalClose}
				turnId={turn.id}
				dayIndex={sessionModal.dayIndex}
				timeIndex={sessionModal.timeIndex}
				preSelectedClient={selectedClient}
				preSelectedWorker={selectedWorker}
				existingSession={getSessionForSlot(sessionModal.dayIndex, sessionModal.timeIndex)}
			/>
		</>
	);
}
