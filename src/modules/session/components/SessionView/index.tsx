import { useState } from "react";
import type { Schedule, Turn } from "~/modules/schedule/service/model/schedule";
import type { Client } from "~/modules/client/service/model/client";
import type { Worker } from "~/modules/worker/service/model/worker";
import ScheduleSelect from "./ScheduleSelect";
import TurnSelect from "./TurnSelect";
import ClientWorkerSelects from "./ClientWorkerSelects";
import SessionScheduleGrid from "./SessionScheduleGrid";

export default function SessionView() {
	const [selectedSchedule, setSelectedSchedule] = useState<Schedule | null>(null);
	const [selectedTurn, setSelectedTurn] = useState<Turn | null>(null);
	const [selectedClient, setSelectedClient] = useState<Client | null>(null);
	const [selectedWorker, setSelectedWorker] = useState<Worker | null>(null);

	const handleScheduleChange = (schedule: Schedule | null) => {
		setSelectedSchedule(schedule);
		setSelectedTurn(null);
		setSelectedClient(null);
		setSelectedWorker(null);
	};

	const handleTurnChange = (turn: Turn | null) => {
		setSelectedTurn(turn);
		setSelectedClient(null);
		setSelectedWorker(null);
	};

	return (
		<div className="space-y-6">
			{/* Schedule Selection */}
			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<h2 className="card-title">Seleccionar Horario</h2>
					<ScheduleSelect
						selectedSchedule={selectedSchedule}
						onScheduleChange={handleScheduleChange}
					/>
				</div>
			</div>

			{/* Turn Selection */}
			{selectedSchedule && (
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<h2 className="card-title">Seleccionar Turno</h2>
						<TurnSelect
							schedule={selectedSchedule}
							selectedTurn={selectedTurn}
							onTurnChange={handleTurnChange}
						/>
					</div>
				</div>
			)}

			{/* Client and Worker Selection */}
			{selectedTurn && (
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<h2 className="card-title">Seleccionar Cliente y Trabajador</h2>
						<ClientWorkerSelects
							selectedClient={selectedClient}
							selectedWorker={selectedWorker}
							onClientChange={setSelectedClient}
							onWorkerChange={setSelectedWorker}
						/>
					</div>
				</div>
			)}

			{/* Schedule Grid */}
			{selectedTurn && selectedSchedule && (
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<h2 className="card-title">Horario de Sesiones</h2>
						<SessionScheduleGrid
							schedule={selectedSchedule}
							turn={selectedTurn}
							selectedClient={selectedClient}
							selectedWorker={selectedWorker}
						/>
					</div>
				</div>
			)}
		</div>
	);
}
