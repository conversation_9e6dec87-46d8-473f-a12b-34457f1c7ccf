import { useState } from "react";
import type { Schedule, Turn } from "~/modules/schedule/service/model/schedule";
import type { Client } from "~/modules/client/service/model/client";
import type { Worker } from "~/modules/worker/service/model/worker";
import ScheduleSelect from "./ScheduleSelect";
import TurnSelect from "./TurnSelect";
import ClientWorkerSelects from "./ClientWorkerSelects";
import SessionScheduleGrid from "./SessionScheduleGrid";

export default function SessionView() {
	const [selectedSchedule, setSelectedSchedule] = useState<Schedule | null>(null);
	const [selectedTurn, setSelectedTurn] = useState<Turn | null>(null);
	const [selectedClient, setSelectedClient] = useState<Client | null>(null);
	const [selectedWorker, setSelectedWorker] = useState<Worker | null>(null);

	const handleScheduleChange = (schedule: Schedule | null) => {
		setSelectedSchedule(schedule);
		setSelectedTurn(null);
		setSelectedClient(null);
		setSelectedWorker(null);
	};

	const handleTurnChange = (turn: Turn | null) => {
		setSelectedTurn(turn);
		setSelectedClient(null);
		setSelectedWorker(null);
	};

	const handleClientChange = (client: Client | null) => {
		setSelectedClient(client);
		// If a client is selected, clear the worker
		if (client) {
			setSelectedWorker(null);
		}
	};

	const handleWorkerChange = (worker: Worker | null) => {
		setSelectedWorker(worker);
		// If a worker is selected, clear the client
		if (worker) {
			setSelectedClient(null);
		}
	};

	return (
		<div className="space-y-6">
			{/* Schedule Selection */}
			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<h2 className="card-title">Seleccionar Horario</h2>
					<ScheduleSelect
						selectedSchedule={selectedSchedule}
						onScheduleChange={handleScheduleChange}
					/>
				</div>
			</div>

			{/* Turn Selection */}
			{selectedSchedule && (
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<h2 className="card-title">Seleccionar Turno</h2>
						<TurnSelect
							schedule={selectedSchedule}
							selectedTurn={selectedTurn}
							onTurnChange={handleTurnChange}
						/>
					</div>
				</div>
			)}

			{/* Client and Worker Selection */}
			{selectedTurn && (
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<h2 className="card-title">Seleccionar Cliente o Trabajador</h2>
						<p className="mb-4 text-base-content/70 text-sm">
							Selecciona un cliente o un trabajador para ver el horario de sesiones
						</p>
						<ClientWorkerSelects
							selectedClient={selectedClient}
							selectedWorker={selectedWorker}
							onClientChange={handleClientChange}
							onWorkerChange={handleWorkerChange}
						/>
					</div>
				</div>
			)}

			{/* Schedule Grid */}
			{selectedTurn && selectedSchedule && (selectedClient || selectedWorker) && (
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<h2 className="card-title">Horario de Sesiones</h2>
						<p className="mb-4 text-base-content/70 text-sm">
							{selectedClient
								? `Asignando sesiones para el cliente: ${selectedClient.person.name} ${selectedClient.person.fatherLastName}`
								: `Asignando sesiones para el trabajador: ${selectedWorker?.person.name} ${selectedWorker?.person.fatherLastName}`
							}
						</p>
						<SessionScheduleGrid
							schedule={selectedSchedule}
							turn={selectedTurn}
							selectedClient={selectedClient}
							selectedWorker={selectedWorker}
						/>
					</div>
				</div>
			)}
		</div>
	);
}
